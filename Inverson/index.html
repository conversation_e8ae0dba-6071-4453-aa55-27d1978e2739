<!DOCTYPE html>
<html lang="es" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Presentación Estratégica - Antigravity, Inc.</title>
    
    <!-- Script de Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Script de Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Swiper's CSS -->
    <link rel="stylesheet" href="https://unpkg.com/swiper/swiper-bundle.min.css" />
    
    <!-- GLightbox's CSS - ELIMINADO
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" />
    -->

    <style type="text/tailwindcss">
        /* Estilo para el canvas de Three.js */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: -1;
        }

        /* Tipografía personalizada */
        h2 {
            @apply text-[28px] font-extrabold tracking-tighter text-[#dbfe0d] text-center mb-6 pb-2 border-b border-gray-700;
        }
        h3 {
            @apply text-2xl font-bold text-[#dbfe0d] mb-4;
        }
        .section-card {
            @apply bg-black/50 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl p-6 sm:p-8;
        }
        .kpi-highlight {
            @apply text-3xl sm:text-4xl font-bold text-[#dbfe0d];
        }

        /* Estilos para la navegación inferior */
        .nav-dot {
            @apply w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center text-xs sm:text-sm font-bold text-gray-400 border border-transparent rounded-full transition-all hover:text-white hover:border-gray-500 cursor-pointer;
        }
        .nav-dot.active {
            @apply text-[#dbfe0d] border-[#dbfe0d] scale-125 font-extrabold bg-gray-900/50;
        }
        /* Estilos para botones anchos */
        .nav-dot-wide {
            @apply w-auto px-3;
        }
        
        header, #bottom-nav {
            transition: opacity 0.3s ease-in-out;
        }

        /* Ocultar contenido principal por defecto para el login */
        #main-content, #bottom-nav, header, footer {
            display: none;
        }
        
        /* Estilos para el Login Overlay */
        #login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: rgba(0,0,0,0.95);
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Estilo para etiquetas strong */
        strong {
            font-weight: bold;
            color: white;
        }
        
        /* Estilos Swiper */
        .swiper-button-next, .swiper-button-prev {
            color: #dbfe0d !important;
        }
        .swiper-pagination-bullet-active {
            background: #dbfe0d !important;
        }
        
        /* Estilos GLightbox - ELIMINADOS 
        .gslide-description {
            background: rgba(0,0,0,0.8) !important;
        }
        .gslide-title {
            color: #dbfe0d !important;
        }
        */

    </style>
</head>
<body class="bg-black text-gray-300 font-sans leading-relaxed">

    <!-- Canvas para el fondo de Three.js -->
    <canvas id="bg-canvas"></canvas>

    <!-- Pantalla de Login -->
    <div id="login-overlay">
        <div class="w-full max-w-sm p-8 text-center">
            <img src="images/antigravity_logo.svg" alt="Logo Antigravity" class="w-48 h-auto w-auto">
            <p class="text-gray-400 mb-6">Ingresa tu correo y el password que se te proporcionó</p>
            <form id="login-form">
                <div class="mb-4">
                    <input id="email" type="email" placeholder="Correo electrónico" required
                           class="w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#dbfe0d] transition-colors">
                </div>
                <div class="mb-6">
                    <input id="password" type="password" placeholder="Password" required
                           class="w-full px-4 py-3 bg-gray-900 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:border-[#dbfe0d] transition-colors">
                </div>
                <button type="submit"
                        class="w-full bg-[#dbfe0d] text-black font-bold py-3 px-6 rounded-lg hover:bg-white transition-colors text-lg">
                    Acceder
                </button>
                <p id="login-error" class="text-red-500 mt-4 text-sm hidden">Password incorrecto. El password es "AntigravitySeed".</p>
            </form>
        </div>
    </div>

    <!-- Menú Overlay (pantalla completa) -->
    <div id="overlay-menu" class="fixed inset-0 bg-black/95 z-40 hidden flex-col items-center justify-center overflow-y-auto p-8">
        <button id="close-menu-btn" class="absolute top-6 right-6 text-gray-400 hover:text-white transition-colors">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
        <nav class="flex flex-col space-y-4 text-center text-xl">
            <a href="#home" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">Inicio</a>
            <a href="#quienes-somos" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">¿Quiénes Somos?</a>
            <a href="#introduccion" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">1. Introducción</a>
            <a href="#financiamiento" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">2. Financiamiento</a>
            <a href="#tesis" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">3. Tesis de Inversión</a>
            <a href="#plataforma" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">4. Plataforma Graviter</a>
            <a href="#mercados" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">5. Mercados</a>
            <a href="#gtm" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">6. Estrategia GTM</a>
            <a href="#regulatoria" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">7. Estrategia Regulatoria</a>
            <a href="#equipo" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">8. Equipo</a>
            <a href="#financieras" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">9. Proyecciones</a>
            <a href="#capital" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">10. Estrategia de Capital</a>
            <a href="#propuesta-valor" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">11. Propuesta de Valor</a>
            <a href="#conclusion" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">12. Conclusión</a>
            <a href="#manifiesto" class="menu-link text-gray-300 hover:text-[#dbfe0d] transition-colors">Manifiesto</a>
        </nav>
    </div>

    <!-- Header (Barra de navegación superior) -->
    <header class="sticky top-0 z-30 w-full bg-black/70 backdrop-blur-md shadow-lg border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Título / Logo -->
                <a href="#home" class="text-xl font-bold text-white">
                    <img src="images/antigravity_logo.svg" alt="Logo Antigravity" class="w-48 h-auto w-auto">
                </a>


                

                <!-- Botón de Menú (móvil) -->
                <button id="open-menu-btn" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-all">
                    <span class="sr-only">Abrir menú</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
        </div>
    </header>

    <!-- Contenedor principal de "diapositivas" -->
    <main id="main-content" class="relative z-10 w-full h-screen overflow-y-scroll snap-y snap-mandatory">

        <!-- Diapositiva 1: Inicio -->
        <section id="home" class="min-h-screen flex items-center justify-center text-center px-4 snap-start">
            <div class="max-w-4xl">
                <h1 class="text-4xl sm:text-6xl lg:text-7xl font-extrabold tracking-tighter text-[#dbfe0d]">
                    Resumen Estratégico

                </h1>
                <p class="mt-4 text-xl sm:text-2xl text-gray-300">
                    Antigravity Company, Inc. (Ronda Seed)
                </p>
                <p class="mt-8 max-w-2xl mx-auto text-lg text-gray-400">
                    Este documento resume los puntos clave del memorándum de inversión, destacando la información más importante de cada sección.
                </p>
            </div>
        </section>

        <!-- Diapositiva 2: ¿Quiénes Somos? -->
        <section id="quienes-somos" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>¿Quiénes Somos?</h2>
                <p class="text-lg text-gray-300 mb-6"><strong>Antigravity Company, Inc.</strong> es una compañía aeroespacial constituida en California, EE. UU. dedicada a diseñar y construir sistemas de vuelo eléctrico (eVTOL) modulares que combinan la precisión y control de un dron con la experiencia humana de volar. Nuestro primer proyecto, <strong>Graviter</strong>, una plataforma que se adapta a múltiples configuraciones para vuelo personal, logística de carga y aplicaciones de seguridad.</p>
                
                <h3 class="mt-6">Misión y Visión</h3>
                <ul class="list-disc list-inside space-y-2 text-lg">
                    <li><strong>Misión:</strong> diseñar sistemas que hagan accesible, seguro y preciso el acto de volar, integrando el dominio técnico de un dron con la emoción humana del vuelo.</li>
                    <li><strong>Visión:</strong> liderar una nueva categoría de dispositivos de vuelo personal y logístico basada en una plataforma modular certificable y eficiente.</li>
                </ul>

                <h3 class="mt-6">Valores</h3>
                <p class="text-lg text-gray-400 italic">Seguridad primero • Precisión • Transparencia • Disciplina de ingeniería • Ejecución sin atajos • Aprendizaje continuo</p>

                <blockquote class="mt-8 text-center text-xl font-semibold text-[#dbfe0d] not-italic border-l-4 border-[#dbfe0d] pl-4">
                    “Aprendimos a volar sin despegar los pies del suelo; ahora queremos construir la manera de despegar de verdad.”
                </blockquote>
            </div>
        </section>

        <!-- Diapositiva 3: Introducción -->
        <section id="introduccion" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>1. Introducción</h2>
                <p class="text-lg text-gray-300"><strong>Antigravity Company, Inc.</strong> es una empresa aeroespacial con base operativa y de distribución en California. Nuestra primera plataforma, <strong>Graviter</strong>, es un eVTOL modular: una arquitectura común de propulsión eléctrica, control de vuelo y estructura que permite configurar dispositivo de vuelo personal, vehículo de carga autónoma y soluciones de seguridad sobre un mismo “chasis” tecnológico.</p>
                <p class="mt-4 text-lg text-gray-300">Para maximizar eficiencia de capital, implementaremos manufactura estratégica en México —optimizando costos de ensamblaje y tiempos de entrega bajo el marco del T-MEC— mientras mantenemos I+D, pruebas, certificación y go-to-market en California.</p>
                
                <h3 class="mt-6">Por qué ahora</h3>
                <p class="text-lg text-gray-300">Tras una década construyendo y operando aeronaves no tripuladas de alta especialización, estamos listos para traducir esa experiencia en una plataforma lista para producción. La demanda global por dispositivos de vuelo personal y por logística aérea de proximidad está validada; el enfoque modular de Graviter acelera el desarrollo, reduce el CAPEX por variante y diversifica el riesgo entre verticales.</p>
                
                <!-- Carrusel Swiper (Introducción) -->
                <div class="intro-swiper swiper-container relative w-full h-64 sm:h-80 mt-6 rounded-lg overflow-hidden border border-gray-700">
                    <div class="swiper-wrapper">
                        <!-- Diapositiva 1 -->
                        <!-- ** CORRECCIÓN: Se cambió <a> por <div>, se quitó glightbox-images, se añadió lightbox-trigger y data-image-src ** -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" 
                             data-image-src="https://placehold.co/1200x800/dbfe0d/000000?text=Diseño+Graviter+1" 
                             data-description="Render inicial de la plataforma Graviter (Configuración Duo).">
                            <img src="https://placehold.co/800x600/dbfe0d/000000?text=Diseño+Graviter+1" alt="Diseño 1" class="w-full h-full object-cover">
                        </div>
                        <!-- Diapositiva 2 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" 
                             data-image-src="https://placehold.co/1200x800/444444/ffffff?text=Prototipo+Chasis" 
                             data-description="Pruebas iniciales del chasis maestro y tren de potencia.">
                            <img src="https://placehold.co/800x600/444444/ffffff?text=Prototipo+Chasis" alt="Diseño 2" class="w-full h-full object-cover">
                        </div>
                        <!-- Diapositiva 3 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" 
                             data-image-src="https://placehold.co/1200x800/111111/dbfe0d?text=Render+Cargo" 
                             data-description="Render de la variante de logística autónoma.">
                            <img src="https://placehold.co/800x600/111111/dbfe0d?text=Render+Cargo" alt="Diseño 3" class="w-full h-full object-cover">
                        </div>
                        <!-- Diapositiva 4 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" 
                             data-image-src="https://placehold.co/1200x800/ffffff/000000?text=Esquema+Técnico" 
                             data-description="Arquitectura de la plataforma modular.">
                            <img src="https://placehold.co/800x600/ffffff/000000?text=Esquema+Técnico" alt="Diseño 4" class="w-full h-full object-cover">
                        </div>
                        <!-- Diapositiva 5 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" 
                             data-image-src="https://placehold.co/1200x800/dbfe0d/000000?text=Diseño+Graviter+2" 
                             data-description="Render inicial de la plataforma Graviter (Configuración Duo).">
                            <img src="https://placehold.co/800x600/dbfe0d/000000?text=Diseño+Graviter+2" alt="Diseño 5" class="w-full h-full object-cover">
                        </div>
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                    <!-- Add Navigation -->
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
                
            </div>
        </section>

        <!-- Diapositiva 4: Financiamiento -->
        <section id="financiamiento" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>2. Etapa de Financiamiento</h2>
                <p class="text-lg text-gray-300 mb-6">Estamos ejecutando una ronda disciplinada para mantener un Cap Table eficiente y estratégico. Buscamos estructurarla con un <strong>Inversor Líder (Lead Investor)</strong> y un grupo compacto de coinversionistas, limitando la participación a un <strong>máximo de 10 inversionistas</strong> en total.</p>
                
                <div class="grid md:grid-cols-2 gap-6">
                    
                    <!-- Opción A -->
                    <div class="border border-[#dbfe0d]/50 bg-[#dbfe0d]/10 rounded-lg p-6">
                        <h3 class="">Opción A (Ruta Preferente)</h3>
                        <p class="kpi-highlight">$750,000 USD</p>
                        <ul class="mt-4 space-y-2 text-gray-300">
                            <li><strong>Runway:</strong> 18 meses.</li>
                            <li><strong>Hitos:</strong> Prototipo completo, primer vuelo, dossier FAA y 3-5 LOIs.</li>
                            <li><strong>Estructura:</strong> 1 Lead ($350k-$500k) + 3-6 co-inversionistas.</li>
                        </ul>
                    </div>
                    
                    <!-- Opción B -->
                    <div class="border border-gray-700 rounded-lg p-6">
                        <h3 class="">Opción B (Puente Técnico)</h3>
                        <p class="kpi-highlight">$250,000 USD</p>
                        <ul class="mt-4 space-y-2 text-gray-300">
                            <li><strong>Runway:</strong> 9-12 meses.</li>
                            <li><strong>Hitos:</strong> Validación HIL, hover test, pre-work regulatorio.</li>
                            <li><strong>Estructura:</strong> 1 Lead ($150k) + 2-4 co-inversionistas.</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-6 bg-gray-800 p-4 rounded-lg">
                    <h3 class="text-xl text-center">Perfil de Inversionista Buscado (Smart Money)</h3>
                    <p class="mt-2 text-center text-gray-300">Socios estratégicos (Ángeles o Fondos Seed) con experiencia en <strong>hardtech, aeroespacial o certificación FAA</strong>, que aporten validación técnica, acceso a proveedores y clientes ancla.</p>
                </div>
            </div>
        </section>

        <!-- Diapositiva 5: Tesis de Inversión -->
        <section id="tesis" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>3. Tesis de Inversión</h2>
                <p class="text-lg text-gray-300">Nuestra tesis se basa en el desarrollo de una <strong>plataforma, no de un producto único</strong>. Este enfoque modular nos permite:</p>
                <ul class="mt-4 space-y-3">
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span class="text-lg">Atacar 3 verticales de mercado (personal, carga, seguridad) simultáneamente.</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span class="text-lg">Reducir drásticamente el <strong>CAPEX por variante</strong>.</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span class="text-lg">Diversificar el riesgo de mercado.</span>
                    </li>
                </ul>
                <p class="mt-6 text-lg text-gray-300">Nuestra ventaja competitiva se fundamenta en <strong>10+ años de ejecución probada</strong> en aeronaves complejas y una <strong>eficiencia operativa binacional (California-México)</strong>.</p>
            </div>
        </section>

        <!-- Diapositiva 6: Plataforma Graviter -->
        <section id="plataforma" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>4. La Plataforma Modular Graviter</h2>
                <p class="text-lg text-gray-300">El <strong>"chasis maestro"</strong> de Graviter unifica propulsión eléctrica redundante, control de vuelo tolerante a fallos, estructura modular e <strong>interfaces estandarizadas</strong>.</p>
                <div class="grid md:grid-cols-2 gap-6 mt-6">
                    <div>
                        <h3 class="">Configuraciones</h3>
                        <ul class="list-disc list-inside text-lg text-gray-300 space-y-1">
                            <li>Duo/Combi (Vuelo Personal)</li>
                            <li>Cargo (Logística)</li>
                            <li>Seguridad</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="">KPI Objetivo</h3>
                        <p class="kpi-highlight">≥70%</p>
                        <p class="text-lg text-gray-300">de reutilización de componentes (BOM núcleo) entre todas las variantes.</p>
                    </div>
                </div>
                <div class="grid md:grid-cols-2 gap-6 mt-6 border-t border-gray-700 pt-6">
                    <div>
                        <h3 class="">Seguridad (Design for Safety)</h3>
                        <ul class="list-disc list-inside text-lg text-gray-300 space-y-1">
                            <li>Redundancias críticas (propulsión, control).</li>
                            <li>Pruebas progresivas (HIL, tierra, vuelo).</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="">Eficiencia de Industrialización</h3>
                        <ul class="list-disc list-inside text-lg text-gray-300 space-y-1">
                            <li>Diseño para Manufactura (DFM/DFA).</li>
                            <li>Footprint CA (I+D) y MX (Ensamble).</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Diapositiva 7: Mercados -->
        <section id="mercados" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>5. Mercados de Oportunidad</h2>
                <p class="text-lg text-gray-300 mb-6">Nuestra plataforma Graviter permite atacar tres verticales estratégicas sobre el mismo "chasis" tecnológico: <strong>vuelo personal</strong>, <strong>logística de carga autónoma</strong> y <strong>seguridad o emergencias</strong>. El enfoque modular reduce el CAPEX por variante, acelera la validación y diversifica el riesgo de mercado.</p>
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="border border-gray-700 rounded-lg p-4">
                        <h3 class="text-xl">1. Vuelo Personal (APR)</h3>
                        <p>Mercado validado (backlogs de 2-3 años en competidores). Precios de $128k-$148k demuestran disposición a pagar.</p>
                    </div>
                    <div class="border border-gray-700 rounded-lg p-4">
                        <h3 class="text-xl">2. Logística (B2B)</h3>
                        <p>Modelo <strong>Hardware-as-a-Service (HaaS)</strong> para sectores críticos: minería, agricultura y logística médica.</p>
                    </div>
                    <div class="border border-gray-700 rounded-lg p-4">
                        <h3 class="text-xl">3. Seguridad (Doble Uso)</h3>
                        <p>Contratos institucionales de alto valor para misiones de vigilancia (ISR) y soporte táctico.</p>
                    </div>
                </div>
                
                <!-- Carrusel Swiper (Mercados) -->
                <div class="markets-swiper swiper-container relative w-full mt-6 rounded-lg overflow-hidden border border-gray-700">
                    <div class="swiper-wrapper">
                        <!-- Video 1 -->
                        <!-- ** CORRECCIÓN: Se cambió <a> por <div>, se quitó glightbox-videos, se añadió lightbox-trigger y data-video-id ** -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" data-video-id="ysE8FMhAPH0">
                            <img src="https://img.youtube.com/vi/ysE8FMhAPH0/hqdefault.jpg" alt="Video Vuelo Personal" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                <svg class="w-12 h-12 text-[#dbfe0d]" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                            </div>
                        </div>
                        <!-- Video 2 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" data-video-id="F-hGReApT0E">
                            <img src="https://img.youtube.com/vi/F-hGReApT0E/hqdefault.jpg" alt="Video Logística" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                <svg class="w-12 h-12 text-[#dbfe0d]" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                            </div>
                        </div>
                        <!-- Video 3 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" data-video-id="vyXy-fSbYyE">
                            <img src="https://img.youtube.com/vi/vyXy-fSbYyE/hqdefault.jpg" alt="Video Pruebas" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                <svg class="w-12 h-12 text-[#dbfe0d]" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                            </div>
                        </div>
                        <!-- Video 4 -->
                        <div class="swiper-slide lightbox-trigger cursor-pointer" data-video-id="J-S6M2XlYgA">
                            <img src="https://img.youtube.com/vi/J-S6M2XlYgA/hqdefault.jpg" alt="Video Vuelo Personal 2" class="w-full h-48 object-cover">
                            <div class="absolute inset-0 bg-black/50 flex items-center justify-center">
                                <svg class="w-12 h-12 text-[#dbfe0d]" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path></svg>
                            </div>
                        </div>
                    </div>
                    <!-- Add Pagination -->
                    <div class="swiper-pagination"></div>
                    <!-- Add Navigation -->
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>

            </div>
        </section>

        <!-- Diapositiva 8: GTM -->
        <section id="gtm" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>6. Estrategia GTM</h2>
                <p class="text-lg text-gray-300 mb-6">Implementaremos un despliegue secuencial: monetizar primero el vuelo personal (lotes limitados), escalar a carga (modelos recurrentes) y expandir a seguridad (contratos). Apalancamos el binomio <strong>CA (I+D/Comercial) + MX (Manufactura)</strong>.</p>
                <div class="flex flex-col md:flex-row md:space-x-6">
                    
                    <!-- Fase 1 -->
                    <div class="flex-1 border-t-4 border-[#dbfe0d] pt-4 mt-4 md:mt-0">
                        <h3 class="text-xl">Fase 1 (0-24m): Validación</h3>
                        <p>Validación de prototipo y <strong>primer vuelo</strong>. Foco en Vuelo Personal (B2C) con "Lotes Founders" y obtención de <strong>3-5 LOIs</strong>.</p>
                    </div>
                    
                    <!-- Fase 2 -->
                    <div class="flex-1 border-t-4 border-gray-600 pt-4 mt-4 md:mt-0">
                        <h3 class="text-gray-400 text-xl">Fase 2 (24-48m): Despliegue B2B</h3>
                        <p>Escalar <strong>Graviter Cargo</strong> con contratos recurrentes (HaaS) y <strong>pilotos B2B operativos</strong> en sectores clave.</p>
                    </div>
                    
                    <!-- Fase 3 -->
                    <div class="flex-1 border-t-4 border-gray-600 pt-4 mt-4 md:mt-0">
                        <h3 class="text-gray-400 text-xl">Fase 3 (48+m): Expansión</h3>
                        <p>Contratos institucionales de alto valor en seguridad y defensa (ISR, soporte logístico).</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Diapositiva 9: Regulatoria -->
        <section id="regulatoria" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>7. Estrategia Regulatoria</h2>
                <p class="text-lg text-gray-300">Nuestra estrategia es <strong>"Compliance-First"</strong>, con un enfoque dual para acelerar la entrada al mercado:</p>
                
                <div class="grid md:grid-cols-2 gap-6 mt-6">
                    <!-- Ruta 1: Ultralight -->
                    <div class="border border-[#dbfe0d]/50 bg-[#dbfe0d]/10 rounded-lg p-6">
                        <h3 class="">Ruta 1: FAA Part 103 (Ultralight)</h3>
                        <p class="text-lg text-gray-300 mt-2">Para la variante de <strong>vuelo personal (monoplaza)</strong>.</p>
                        <ul class="list-disc list-inside mt-4 text-gray-300">
                            <li><strong>Ventaja Clave:</strong> No requiere certificación de aeronave.</li>
                            <li><strong>Operador:</strong> No requiere licencia de piloto.</li>
                            <li><strong>Mercado:</strong> Permite ventas B2C tempranas y validación rápida.</li>
                        </ul>
                    </div>
                    
                    <!-- Ruta 2: LSA -->
                    <div class="border border-gray-700 rounded-lg p-6">
                        <h3 class="">Ruta 2: Light Sport Aircraft (LSA)</h3>
                        <p class="text-lg text-gray-300 mt-2">Para variantes <strong>biplaza (Duo)</strong> y de <strong>Carga (Cargo)</strong>.</p>
                        <ul class="list-disc list-inside mt-4 text-gray-300">
                            <li><strong>Ventaja Clave:</strong> Permite operaciones comerciales (carga) y de instrucción.</li>
                            <li><strong>Proceso:</strong> Ruta de certificación definida y más ágil que la aviación tradicional.</li>
                            <li><strong>Mercado:</strong> Escala el negocio a B2B (HaaS) y seguridad.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Diapositiva 10: Equipo -->
        <section id="equipo" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>8. Equipo Directivo</h2>
                <p class="text-lg text-gray-300 mb-6">El liderazgo probado es clave. El núcleo está compuesto por:</p>
                <ul class="space-y-4">
                    <li>
                        <h3 class="text-xl">CEO — Gibran Viveros Yañez</h3>
                        <p>Fundador de Dronexperto, con más de una década de experiencia comprobada diseñando y operando aeronaves no tripuladas de alta complejidad.</p>
                    </li>
                    <li>
                        <h3 class="text-xl">CTO (Perfil Designado)</h3>
                        <p>Contratación clave para ser el arquitecto de la plataforma.</p>
                    </li>
                    <li>
                        <h3 class="text-xl">COO (Perfil Designado)</h3>
                        <p>Contratación clave para liderar la industrialización y la cadena de suministro (CA-MX).</p>
                    </li>
                </ul>
                <p class="mt-6 text-lg text-gray-300">El equipo estará respaldado por un <strong>Comité Asesor</strong> especializado.</p>
            </div>
        </section>

        <!-- Diapositiva 11: Proyecciones -->
        <section id="financieras" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>9. Proyecciones Financieras y Solicitud</h2>
                <p class="text-lg text-gray-300 mb-6">Ronda Seed pre-ingresos enfocada 100% en validación técnica y comercial.</p>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Opción A -->
                    <div class="border border-[#dbfe0d]/50 bg-[#dbfe0d]/10 rounded-lg p-6">
                        <h3 class="">Opción A (Seed Completa)</h3>
                        <p class="kpi-highlight">$750,000 USD</p>
                        <p class="text-lg text-gray-300"><strong>Runway:</strong> 18 meses</p>
                        <p class="text-lg text-gray-300"><strong>Burn Promedio:</strong> ~$41.7k/mes</p>
                        <h4 class="font-bold text-white mt-4 mb-2">Uso de Fondos:</h4>
                        <ul class="list-disc list-inside text-gray-300">
                            <li>I+D y Prototipo: 50%</li>
                            <li>Certificación y PI: 20%</li>
                            <li>Des. de Negocio (LOIs): 15%</li>
                            <li>Operaciones: 15%</li>
                        </ul>
                    </div>
                    
                    <!-- Opción B -->
                    <div class="border border-gray-700 rounded-lg p-6">
                        <h3 class="">Opción B (Puente Técnico)</h3>
                        <p class="kpi-highlight">$250,000 USD</p>
                        <p class="text-lg text-gray-300"><strong>Runway:</strong> 9-12 meses</p>
                        <p class="text-lg text-gray-300"><strong>Burn Promedio:</strong> ~$21k-28k/mes</p>
                        <h4 class="font-bold text-white mt-4 mb-2">Uso de Fondos:</h4>
                        <ul class="list-disc list-inside text-gray-300">
                            <li>I+D Inicial y Pruebas: 60%</li>
                            <li>Ingeniería y Soporte: 25%</li>
                            <li>Operación Mínima: 15%</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-6 bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl text-center mb-4">"Gatillos" Clave para Serie A</h3>
                    <ul class="list-disc list-inside text-gray-300 space-y-2 md:columns-2">
                        <li><strong>3-5 LOIs</strong> (B2C/B2B) activas.</li>
                        <li>Horas de vuelo acumuladas.</li>
                        <li>Dossier FAA inicial presentado.</li>
                        <li>BOM núcleo <strong>reutilizable ≥70%</strong>.</li>
                        <li>Línea piloto (MX) lista para escalar.</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Diapositiva 12: Capital -->
        <section id="capital" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>10. Estrategia de Capital (Due-Diligence)</h2>
                <p class="text-lg text-gray-300 mb-6">Proceso enfocado en un <strong>máximo de 10 inversionistas</strong>, priorizando un Lead Investor estratégico.</p>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="">Composición (Tickets Guía)</h3>
                        <ul class="list-disc list-inside text-lg text-gray-300 space-y-2">
                            <li><strong>Opción A Lead:</strong> $350k - $500k</li>
                            <li><strong>Opción A Co-inversor:</strong> $50k - $100k</li>
                            <li><strong>Opción B Lead:</strong> $150k</li>
                            <li><strong>Opción B Co-inversor:</strong> $25k - $50k</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="">Términos y Gobernanza</h3>
                        <ul class="list-disc list-inside text-lg text-gray-300 space-y-2">
                            <li><strong>Instrumento:</strong> SAFE post-money (con derecho pro-rata) o MFN SAFE.</li>
                            <li><strong>Gobernanza:</strong> Consejo de 3 (2 Fundadores + 1 Lead) o Puesto de Observador.</li>
                            <li><strong>Reporting:</strong> KPI mensual.</li>
                        </ul>
                    </div>
                </div>
                <div class="mt-6 bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl text-center mb-4">Liberación de Fondos Condicionada (Gate Reviews)</h3>
                    <ul class="list-disc list-inside text-gray-300 space-y-2">
                        <li><strong>Tramo 1 (Hito):</strong> HIL completo y ensayos de tierra críticos.</li>
                        <li><strong>Tramo 2 (Hito):</strong> Hover test exitoso y validación de estabilidad.</li>
                        <li><strong>Tramo 3 (Hito):</strong> Dossier FAA v0 entregado y 3-5 LOIs obtenidas.</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Diapositiva 13: Propuesta de Valor -->
        <section id="propuesta-valor" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>11. Propuesta de Valor Estratégico</h2>
                <p class="text-lg text-gray-300 mb-6">Nuestra propuesta de valor central es una <strong>Eficiencia de Capital Superior</strong>.</p>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>
                        <span class="text-lg"><strong>Enfoque de Plataforma:</strong> Un solo I+D desbloquea 3 verticales de mercado, reduciendo el CAPEX.</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>
                        <span class="text-lg"><strong>Footprint Óptimo (CA-MX):</strong> Ahorros estimados del 40-60% en costos de ensamblaje.</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-6 h-6 text-[#dbfe0d] flex-shrink-0 mr-3 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span class="text-lg"><strong>Riesgo de Ejecución Reducido:</strong> El know-how de 10+ años permite un <strong>Time-to-Market más rápido</strong>.</span>
                    </li>
                </ul>
            </div>
        </section>

        <!-- Diapositiva 14: Conclusión -->
        <section id="conclusion" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl">
                <h2>12. Conclusión</h2>
                <p class="text-lg text-gray-300">Antigravity Company, Inc. está preparada para convertir el capital Seed en pruebas de vuelo exitosas, entregables regulatorios concretos y demanda comercial comprometida. Con <strong>California como centro de validación</strong> y <strong>México como palanca de manufactura</strong>, Graviter es la base para escalar con eficiencia y asumir el liderazgo en vuelo personal, carga autónoma y seguridad.</p>
                
                <div class="mt-8 bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl text-center mb-4">Llamado a la Acción</h3>
                    <ul class="list-disc list-inside text-gray-300 space-y-2 text-center">
                        <li><strong>Acceso a Data Room</strong> (Bajo NDA).</li>
                        <li><strong>Revisión Técnica/Regulatoria</strong> (Sesiones Deep-Dive).</li>
                        <li><strong>Demos Privadas</strong> en California (Hover tests).</li>
                    </ul>
                    <p class="text-center mt-4 text-[#dbfe0d] font-bold text-lg"><EMAIL></p>
                </div>
            </div>
        </section>

        <!-- Diapositiva 15: Manifiesto -->
        <section id="manifiesto" class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8 snap-start">
            <div class="section-card w-full max-w-5xl bg-gray-900 border-[#dbfe0d]/50">
                <h2>Manifiesto del Fundador</h2>
                <p class="text-lg text-gray-300 italic">La visión de la compañía es la culminación de 10 años de experiencia. El objetivo es <strong>fusionar la precisión algorítmica de un dron con la experiencia visceral y directa del vuelo humano</strong> (inspirada en la sensación del parapente). Graviter es el sistema para hacer el sueño de volar una realidad controlada, segura y alcanzable.</p>
                <blockquote class="mt-6 text-right text-2xl font-semibold text-[#dbfe0d] not-italic">
                    “Yo no sueño con volar. Yo diseño cómo hacerlo posible.”
                </blockquote>
            </div>
        </section>

    </main> 

    <!-- ** NUEVO: Modal Personalizado para Lightbox ** -->
    <div id="lightbox-modal" class="fixed inset-0 bg-black/95 z-50 hidden items-center justify-center p-4 transition-opacity duration-300">
        <!-- Contenedor del contenido -->
        <div class="relative w-full max-w-4xl h-auto shadow-2xl">
            
            <!-- Contenedor para Imagen -->
            <div id="lightbox-image-container" class="hidden w-full">
                <img id="lightbox-image" src="" alt="Vista ampliada" class="w-full h-auto max-h-[80vh] object-contain rounded-lg">
                <p id="lightbox-image-description" class="text-center text-gray-300 mt-3 text-sm"></p>
            </div>
            
            <!-- Contenedor para Video -->
            <div id="lightbox-video-container" class="hidden w-full aspect-video">
                <iframe id="lightbox-iframe" class="absolute inset-0 w-full h-full" 
                        src="" 
                        title="YouTube video player"
                        frameborder="0" 
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                        allowfullscreen>
                </iframe>
            </div>

        </div>
        <!-- Botón de Cierre -->
        <button id="close-lightbox-modal" class="absolute top-4 right-4 sm:top-6 sm:right-6 text-white hover:text-[#dbfe0d] transition-colors" title="Cerrar">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
    </div>
    <!-- ** FIN del Modal Personalizado ** -->


    <!-- Navegación inferior fija -->
    <nav id="bottom-nav" class="fixed bottom-0 left-1/2 -translate-x-1/2 z-30 p-2 sm:p-4">
        <div class="flex flex-wrap justify-center gap-1 sm:gap-2 bg-black/50 backdrop-blur-md border border-gray-800 rounded-full px-3 py-2 sm:px-4 sm:py-2">
            <a href="#home" class="nav-dot nav-dot-wide" data-target="#home" title="Inicio">Inicio</a>
            <a href="#quienes-somos" class="nav-dot nav-dot-wide" data-target="#quienes-somos" title="¿Quiénes Somos?">Antigravity</a>
            <a href="#introduccion" class="nav-dot" data-target="#introduccion" title="1. Introducción">1</a>
            <a href="#financiamiento" class="nav-dot" data-target="#financiamiento" title="2. Financiamiento">2</a>
            <a href="#tesis" class="nav-dot" data-target="#tesis" title="3. Tesis">3</a>
            <a href="#plataforma" class="nav-dot" data-target="#plataforma" title="4. Plataforma">4</a>
            <a href="#mercados" class="nav-dot" data-target="#mercados" title="5. Mercados">5</a>
            <a href="#gtm" class="nav-dot" data-target="#gtm" title="6. GTM">6</a>
            <a href="#regulatoria" class="nav-dot" data-target="#regulatoria" title="7. Regulatoria">7</a>
            <a href="#equipo" class="nav-dot" data-target="#equipo" title="8. Equipo">8</a>
            <a href="#financieras" class="nav-dot" data-target="#financieras" title="9. Proyecciones">9</a>
            <a href="#capital" class="nav-dot" data-target="#capital" title="10. Capital">10</a>
            <a href="#propuesta-valor" class="nav-dot" data-target="#propuesta-valor" title="11. Valor">11</a>
            <a href="#conclusion" class="nav-dot" data-target="#conclusion" title="12. Conclusión">12</a>
            <a href="#manifiesto" class="nav-dot nav-dot-wide" data-target="#manifiesto" title="Manifiesto">Manifiesto</a>
        </div>
    </nav>
    
    
    <!-- Footer -->
    <footer class="relative z-10 border-t border-gray-800 bg-black/80">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 text-center text-gray-500 text-sm">
            <p>&copy; 2025 Antigravity Company, Inc. Todos los derechos reservados.</p>
            <p class="mt-1">Resumen Estratégico. Solo para fines informativos.</p>
        </div>
    </footer>


    <!-- Swiper's JS -->
    <script src="https://unpkg.com/swiper/swiper-bundle.min.js"></script>
    
    <!-- GLightbox's JS - ELIMINADO
    <script src="https://cdn.jsdelivr.net/gh/mcstudios/glightbox/dist/js/glightbox.min.js"></script>
    -->
    
    <!-- Script principal de la aplicación -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            
            // Declaraciones de elementos globales
            const mainContainer = document.getElementById('main-content');
            const sections = document.querySelectorAll('#main-content > section');
            const navDots = document.querySelectorAll('.nav-dot');
            const headerEl = document.querySelector('header');
            const bottomNavEl = document.getElementById('bottom-nav');
            const footerEl = document.querySelector('footer');
            const loginOverlay = document.getElementById('login-overlay');
            const loginForm = document.getElementById('login-form');
            const passwordInput = document.getElementById('password');
            const loginError = document.getElementById('login-error');

            /* --- Lógica del Formulario de Login --- */
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // Password correcto
                if (passwordInput.value === 'AntigravitySeed') {
                    loginOverlay.style.display = 'none';
                    // Mostrar contenido principal
                    mainContainer.style.display = 'block';
                    bottomNavEl.style.display = 'flex';
                    headerEl.style.display = 'block';
                    footerEl.style.display = 'block';
                    
                    // Inicializar lógicas de la página principal
                    initScrollLogic();
                    initThree();
                    // initGalleries(); // <-- ELIMINADO
                    initSwipers(); // <-- NUEVO
                    initCustomLightbox(); // <-- NUEVO
                    
                    mainContainer.addEventListener('scroll', () => {
                        // Evitar error si mainContainer.scrollHeight es 0
                        if (mainContainer.scrollHeight > mainContainer.clientHeight) {
                            const scrollPercentage = mainContainer.scrollTop / (mainContainer.scrollHeight - mainContainer.clientHeight);
                            scrollYOffset = -scrollPercentage * 200;
                        }
                    });

                    window.addEventListener('resize', onWindowResize);
                    window.addEventListener('mousemove', onMouseMove); 

                } else {
                    // Password incorrecto
                    loginError.classList.remove('hidden');
                    passwordInput.focus();
                }
            });

            /* --- Lógica del Menú de Navegación --- */
            const openMenuBtn = document.getElementById('open-menu-btn');
            const closeMenuBtn = document.getElementById('close-menu-btn');
            const overlayMenu = document.getElementById('overlay-menu');
            const menuLinks = document.querySelectorAll('.menu-link');

            function toggleMenu(visible) {
                overlayMenu.classList.toggle('hidden', !visible);
                overlayMenu.classList.toggle('flex', visible);
                mainContainer.style.overflowY = visible ? 'hidden' : 'scroll';
            }

            openMenuBtn.addEventListener('click', () => toggleMenu(true));
            closeMenuBtn.addEventListener('click', () => toggleMenu(false));
            menuLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    toggleMenu(false);
                    e.preventDefault();
                    const targetId = link.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            /* --- Lógica de Navegación por Scroll (Intersection Observer) --- */
            function initScrollLogic() {
                // Set initial state (hidden on first slide)
                headerEl.style.opacity = '0';
                headerEl.style.pointerEvents = 'none';
                bottomNavEl.style.opacity = '0';
                bottomNavEl.style.pointerEvents = 'none';

                const observerOptions = {
                    root: mainContainer, 
                    rootMargin: '0px',
                    threshold: 0.5 // Umbral del 50%
                };

                const scrollObserver = new IntersectionObserver((entries, obs) => {
                    entries.forEach(entry => {
                        const targetDot = document.querySelector(`.nav-dot[data-target="#${entry.target.id}"]`);
                        if (!targetDot) return; 

                        if (entry.isIntersecting) {
                            // La diapositiva está entrando o es la más visible
                            navDots.forEach(dot => dot.classList.remove('active')); // Limpiar todos
                            targetDot.classList.add('active'); // Activar el actual
                            
                            // Lógica para mostrar/ocultar UI
                            if (entry.target.id === 'home') {
                                headerEl.style.opacity = '0';
                                headerEl.style.pointerEvents = 'none';
                                bottomNavEl.style.opacity = '0';
                                bottomNavEl.style.pointerEvents = 'none';
                            } else {
                                headerEl.style.opacity = '1';
                                headerEl.style.pointerEvents = 'auto';
                                bottomNavEl.style.opacity = '1';
                                bottomNavEl.style.pointerEvents = 'auto';
                            }
                        }
                    });
                }, observerOptions);

                sections.forEach(section => {
                    scrollObserver.observe(section);
                });
            }

            
            /* --- Lógica de Sliders y Lightbox --- */
            // ** CORRECCIÓN: Se eliminó initGalleries() y se reemplazó por dos nuevas funciones **

            function initSwipers() {
                const introSwiper = new Swiper('.intro-swiper', {
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                });

                const marketsSwiper = new Swiper('.markets-swiper', {
                    slidesPerView: 1,
                    spaceBetween: 20,
                    loop: true,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    breakpoints: {
                        640: {
                            slidesPerView: 2,
                            spaceBetween: 20,
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 30,
                        },
                    },
                });
            }

            function initCustomLightbox() {
                const modal = document.getElementById('lightbox-modal');
                if (!modal) return; // Salir si el modal no existe

                const closeBtn = document.getElementById('close-lightbox-modal');
                
                const imageContainer = document.getElementById('lightbox-image-container');
                const modalImage = document.getElementById('lightbox-image');
                const modalImageDesc = document.getElementById('lightbox-image-description');
                
                const videoContainer = document.getElementById('lightbox-video-container');
                const modalIframe = document.getElementById('lightbox-iframe');

                const triggers = document.querySelectorAll('.lightbox-trigger');

                triggers.forEach(trigger => {
                    trigger.addEventListener('click', () => {
                        const videoId = trigger.dataset.videoId;
                        const imageSrc = trigger.dataset.imageSrc;

                        if (videoId) {
                            // Es un video
                            modalIframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
                            videoContainer.classList.remove('hidden');
                            imageContainer.classList.add('hidden');
                        } else if (imageSrc) {
                            // Es una imagen
                            modalImage.src = imageSrc;
                            modalImageDesc.textContent = trigger.dataset.description || '';
                            imageContainer.classList.remove('hidden');
                            videoContainer.classList.add('hidden');
                        }
                        
                        modal.classList.remove('hidden');
                        modal.classList.add('flex');
                    });
                });

                function closeModal() {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    // IMPORTANTE: Detener video
                    modalIframe.src = '';
                    // Limpiar imagen
                    modalImage.src = '';
                    modalImageDesc.textContent = '';
                }

                closeBtn.addEventListener('click', closeModal);
                modal.addEventListener('click', (e) => {
                    // Cerrar si se hace clic en el fondo oscuro
                    if (e.target === modal) {
                        closeModal();
                    }
                });
            }
            
            /* --- Lógica de Three.js para el Fondo --- */
            let scene, camera, renderer, stars;
            let pyramids = [];
            let mouse = new THREE.Vector2();
            let scrollYOffset = 0;

            function initThree() {
                scene = new THREE.Scene();
                camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);
                camera.position.z = 10; 

                const canvas = document.getElementById('bg-canvas');
                renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    alpha: true 
                });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

                // Estrellas
                const starGeometry = new THREE.BufferGeometry();
                const starMaterial = new THREE.PointsMaterial({
                    color: 0xffffff,
                    size: 0.1,
                    transparent: true,
                    opacity: 0.8
                });

                const starVertices = [];
                for (let i = 0; i < 15000; i++) { 
                    const x = (Math.random() - 0.5) * 2000;
                    const y = (Math.random() - 0.5) * 2000;
                    const z = (Math.random() - 0.5) * 2000;
                    starVertices.push(x, y, z);
                }

                starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
                stars = new THREE.Points(starGeometry, starMaterial);
                scene.add(stars);

                // Pirámides
                const pyramidMaterial = new THREE.MeshPhongMaterial({ 
                    color: 0xdbfe0d,
                    flatShading: true,
                    transparent: true,
                    opacity: 0.7
                });

                for (let i = 0; i < 150; i++) { 
                    const radius = (Math.random() * 0.5 + 0.2) * 15; 
                    const height = radius * (1.5 + Math.random() * 0.5); 
                    const pyramidGeometry = new THREE.ConeGeometry(radius, height, 4); 

                    const pyramid = new THREE.Mesh(pyramidGeometry, pyramidMaterial);

                    pyramid.position.set(
                        (Math.random() - 0.5) * 400,
                        (Math.random() - 0.5) * 400,
                        (Math.random() - 0.5) * 400
                    );

                    pyramid.rotation.x = Math.random() * Math.PI * 2;
                    pyramid.rotation.y = Math.random() * Math.PI * 2;
                    pyramid.rotation.z = Math.random() * Math.PI * 2;

                    pyramid.userData.rotationSpeed = {
                        x: (Math.random() - 0.5) * 0.005,
                        y: (Math.random() - 0.5) * 0.005,
                        z: (Math.random() - 0.5) * 0.005
                    };

                    scene.add(pyramid);
                    pyramids.push(pyramid);
                }

                // Luces
                const ambientLight = new THREE.AmbientLight(0x404040, 2); 
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1); 
                directionalLight.position.set(1, 1, 1).normalize();
                scene.add(directionalLight);


                animate();
            }

            function animate() {
                requestAnimationFrame(animate);
                
                const targetX = mouse.x * 5; 
                const targetY = scrollYOffset + (mouse.y * 5); 
                
                // Asegurarse de que la cámara exista antes de usarla
                if (camera) {
                    camera.position.x += (targetX - camera.position.x) * 0.05;
                    camera.position.y += (targetY - camera.position.y) * 0.05;
                    camera.lookAt(scene.position); 
                }

                if (stars) {
                    stars.rotation.z += 0.00005; 
                    stars.position.z += 0.005; 
                    if (stars.position.z > 50) { 
                       stars.position.z = -200; 
                    }
                }

                pyramids.forEach(pyramid => {
                    pyramid.rotation.x += pyramid.userData.rotationSpeed.x;
                    pyramid.rotation.y += pyramid.userData.rotationSpeed.y;
                    pyramid.rotation.z += pyramid.userData.rotationSpeed.z;
                });

                if (renderer && scene && camera) {
                    renderer.render(scene, camera);
                }
            }

            function onWindowResize() {
                // Asegurarse de que la cámara y el renderer existan
                if (camera && renderer) {
                    camera.aspect = window.innerWidth / window.innerHeight;
                    camera.updateProjectionMatrix();
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                }
            }
            
            function onMouseMove(event) {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            }

        });
    </script>

</body>
</html>




